{"name": "management", "version": "0.0.0", "description": "This is the description of the plugin.", "strapi": {"name": "management", "description": "Description of management plugin", "kind": "plugin"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@strapi/design-system": "^1.6.3", "@strapi/helper-plugin": "^4.6.0", "@strapi/icons": "^1.6.3", "antd": "^5.26.6", "lucide-react": "^0.526.0", "prop-types": "^15.7.2", "react-quill": "^2.0.0", "recharts": "^2.12.7"}, "devDependencies": {"@strapi/typescript-utils": "^4.6.0", "@types/react": "^17.0.53", "@types/react-dom": "^18.0.28", "@types/styled-components": "^5.1.26", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.26.1", "styled-components": "^5.3.6", "typescript": "5.0.4"}, "peerDependencies": {"react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0", "react-router-dom": "^6.26.1", "styled-components": "^5.3.6"}, "author": {"name": "A Strapi developer"}, "maintainers": [{"name": "A Strapi developer"}], "engines": {"node": ">=16.0.0 <=20.x.x", "npm": ">=6.0.0"}, "scripts": {"develop": "tsc -p tsconfig.server.json -w", "build": "tsc -p tsconfig.server.json"}, "license": "MIT"}